import { expect } from '@playwright/test'
import { test } from '../utils/test'
import { completeSurvey } from '../utils/survey-helpers'
import {
  createAuthenticatedUser,
  createPrefilledSurvey,
  createUser,
} from '../utils/user-helpers'
import { faker } from '@faker-js/faker'

test.describe('Company Variation Benchmark', () => {
  test('It should notify all company members when the inner company threshold is reached', async ({
    page,
    supawright,
    email,
  }) => {
    test.setTimeout(120_000)

    // Create a company
    const company = await supawright.create('companies', {
      name: faker.company.name(),
      industry: 'Technology',
      employee_count: '11-50',
      country: 'Switzerland',
    })

    // Create 4 users with profiles and prefilled surveys (threshold is 5, so we need 4 + 1 = 5)
    await Promise.all(
      Array.from({ length: 4 }, async () => {
        const { user } = await createUser(supawright, company.id)
        await createPrefilledSurvey(supawright, user.id)
      }),
    )

    // Create the 5th user who will trigger the threshold
    await createAuthenticatedUser(
      { supawright, page },
      { companyId: company.id },
    )

    // Start a new survey
    await page.getByRole('button', { name: 'New Assessment' }).first().click()

    // Complete the survey (this should trigger the inner company notification)
    await completeSurvey(page)

    await page.waitForURL('**/results')

    // Verify the 5th survey was completed
    const allSurveys = await supawright
      .supabase('public')
      .from('survey_responses')
      .select('*', { count: 'exact' })
      .eq('company_id', company.id)
      .not('completed_at', 'is', null)

    expect(allSurveys.count).toBe(5)

    // Get all company member emails for verification
    const companyMembers = await supawright
      .supabase('public')
      .from('profiles')
      .select('email')
      .eq('company_id', company.id)

    const memberEmails =
      companyMembers.data?.map((member) => member.email as string) ?? []
    expect(memberEmails).toHaveLength(5)

    // Wait for and verify that notification emails were sent
    const emails = await email.waitForEmails(
      memberEmails,
      'has unlocked company variation insights',
    )

    expect(emails.length).toBeGreaterThan(0)

    // Verify email content
    const firstEmail = emails[0]
    expect(firstEmail.Subject).toContain(
      'has unlocked company variation insights',
    )

    // Check if company was marked as notified (this happens when workflow completes)
    const companyAfterWait = await supawright
      .supabase('public')
      .from('companies')
      .select('inner_company_threshold_notified_at')
      .eq('id', company.id)
      .single()

    expect(
      companyAfterWait.data?.inner_company_threshold_notified_at,
      'Notified at should be set when threshold is reached',
    ).toBeTruthy()

    // Verify that the inner benchmark is now unlocked
    await page.getByRole('tab', { name: 'Company Variation' }).click()
    await expect(page.getByText('Company Variation Analysis')).toBeVisible()
  })
})
