import { expect } from '@playwright/test'
import { test } from '../utils/test'
import { completeSurvey } from '../utils/survey-helpers'
import {
  createAuthenticatedUser,
  createPrefilledSurvey,
  createUser,
} from '../utils/user-helpers'
import { faker } from '@faker-js/faker'

test.describe.serial('Report Generation Threshold', () => {
  test('It should notify all company members when the report generation threshold is reached', async ({
    page,
    supawright,
    email,
  }) => {
    test.setTimeout(120_000)

    const company = await supawright.create('companies', {
      name: faker.company.name(),
      inner_company_threshold_notified_at: faker.date.past().toISOString(),
      industry_threshold_notified_at: faker.date.past().toISOString(),
      report_threshold_notified_at: null,
    })

    // Create 14 users with profiles and prefilled surveys (threshold is 15, so we need 14 + 1 = 15)
    await Promise.all(
      Array.from({ length: 14 }, async () => {
        const { user } = await createUser(supawright, company.id)
        await createPrefilledSurvey(supawright, user.id)
      }),
    )

    // Create the 15th user (this will trigger the report generation notification)
    await createAuthenticatedUser(
      { supawright, page },
      { companyId: company.id },
    )

    // Start a new survey
    await page.getByRole('button', { name: 'New Assessment' }).first().click()

    // Complete the survey (this should trigger the report generation notification)
    await completeSurvey(page)

    await page.waitForURL('**/results')
    await page.waitForTimeout(1_000)

    // Get all company member emails
    const companyMembers = await supawright
      .supabase('public')
      .from('profiles')
      .select('email')
      .eq('company_id', company.id)

    const memberEmails =
      companyMembers.data?.map((member) => member.email as string) ?? []
    expect(memberEmails).toHaveLength(15) // 14 + 1 new user

    // Wait for and verify that report generation notification emails were sent
    const emails = await email.waitForEmails(
      memberEmails,
      'AI Readiness Report is Ready',
    )

    expect(emails.length).toBeGreaterThan(0)

    // Verify email content
    const firstEmail = emails[0]
    expect(firstEmail.Subject).toContain('AI Readiness Report is Ready')

    await page.waitForTimeout(1_000)

    // Check that the company was marked as notified
    const companyAfterWait = await supawright
      .supabase('public')
      .from('companies')
      .select('report_threshold_notified_at')
      .eq('id', company.id)
      .single()

    expect(companyAfterWait.data?.report_threshold_notified_at).toBeTruthy()
  })
})
