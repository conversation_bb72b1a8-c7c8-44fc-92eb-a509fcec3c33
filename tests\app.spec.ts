import { test, expect } from '@playwright/test'

test.describe('Application Navigation', () => {
  test('homepage loads correctly', async ({ page }) => {
    await page.goto('/')

    // Check page title
    await expect(page).toHaveTitle(/readyai\.one - AI Readiness Assessment/)

    // Check that the main content is visible
    await expect(page.locator('body')).toBeVisible()
  })

  test('404 page works for invalid routes', async ({ page }) => {
    await page.goto('/invalid-route-that-does-not-exist')

    // Should show 404 or not found content
    const notFoundContent = page.locator('text=/not found|404/i').first()
    if (await notFoundContent.isVisible()) {
      await expect(notFoundContent).toBeVisible()
    }
  })
})

test.describe('API Integration', () => {
  test('API server is running', async ({ page }) => {
    // Test that the API server is accessible
    const response = await page.request.get('http://localhost:8080/api')

    // Should get some response (even 404 is fine, means server is running)
    expect(response.status()).toBeLessThan(500)
  })
})
