/* eslint-disable prettier/prettier */

import { tool } from 'ai'
import { z } from 'zod'
import {
  SurveyService,
  type CompanyToolData,
} from '../../services/survey-service'

const ToolUseCase = z.object({
  tool: z.string().describe('The name of the tool (e.g., "<PERSON><PERSON><PERSON>", "<PERSON>")'),
  use_case: z.string().describe('How the tool is used (e.g., "Coding", "Resume Shortlisting")'),
})
const ParamsSchema = z.object({
  confirmedTools: z.array(ToolUseCase).describe('Array of tool/use_case pairs to ask about'),
  reviews: z.string().describe('What the user has heard about these tools from other employees'),
  userId: z.string().describe('The authenticated user ID (optional for anonymous users)'),
})
type Params = z.infer<typeof ParamsSchema>

export const ConfirmUsageClaimsTool = tool({
  description: `Confirm whether tools and usecases in CompanyData are correctly claimed.
 Use tool AFTER get units and departments using AI tool.
 
❌ NEVER mention company name directly.
❌ Avoid saying "everyone uses it".
✅ Always ask in a friendly, short way.
✅ Put each question in a new line.
✅ Cross-reference user claims with actual company data.
✅ Mention only one tool/use_case combo per message.

Example:
  AI:" We've heard that people in your company use Cursor for Coding, Gemini for Resume Shortlisting. Can you confirm usage of such tools? Do you use any of these tools?"
  User: "Yes, I have heard of these tools and I use them daily."
  AI: " What do the people in company have to say about these tools?"

  Note-
  This tool is ONLY for verifying claims against company data.
  Do NOT use this data in other tools or general conversation.
  The company tool data should remain isolated to this specific verification process.
  `,
  inputSchema: ParamsSchema,

  // ensure params is typed from the schema so the execute signature matches the tool overloads
  execute: async (params: Params) => {
    const { confirmedTools, userId, reviews } = params

    if (!userId || userId === 'anonymous') {
      return 'To verify company-specific tool usage, please log in to your account.'
    }

    const surveyService = new SurveyService()
    const companyData = await surveyService.fetchAndStoreCompanyData(userId)

    if (!companyData || !companyData.tool_and_usecases || companyData.tool_and_usecases.length === 0) {
      return 'No tools or use cases found for your company.'
    }
    const level=companyData.level
    companyData.tool_and_usecases//const tools_usecases_to_ask = companyData.tool_and_usecases
      .slice(0, 2) // Limit to 2 for brevity
      .map(
        (item: { tool: string; use_case: string }) =>
          `Employees in your company claim to use ${item.tool} for ${item.use_case}. Have you heard of such usage of tools?`
      )

    let response = companyData.tool_and_usecases.join('\n\n')

    // Ask what the user has heard from other employees
    if (reviews && reviews.trim() !== '') {
      response += `\n\nWhat have you heard from other employees about these tools? ${reviews}`
    } else {
      response += `\n\nWhat have you heard from other employees about these tools?`
    }

    return response
  },
})
