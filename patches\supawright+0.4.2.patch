diff --git a/node_modules/supawright/dist/index.js b/node_modules/supawright/dist/index.js
index b3dc688..ca8ad1a 100644
--- a/node_modules/supawright/dist/index.js
+++ b/node_modules/supawright/dist/index.js
@@ -490,29 +490,48 @@ var Supawright = class _Supawright {
         throw new Error(`Could not find table definition for ${qualifiedTable}`);
       }
       const supabase2 = this.supabase(schema);
-      let deletionQuery = supabase2.from(table).delete();
       if (tableDefinition.primaryKeys.length > 0) {
-        const filterStrings = [];
-        for (const fixture of fixtures) {
-          const filterString = tableDefinition.primaryKeys.map((key) => `${key}.eq.${fixture.data[key]}`).join(",");
-          if (tableDefinition.primaryKeys.length > 1) {
-            filterStrings.push(`and(${filterString})`);
-          } else {
-            filterStrings.push(filterString);
+        // Use 'in' operator to avoid URI too long errors while maintaining deletion order
+        if (tableDefinition.primaryKeys.length === 1) {
+          // Single primary key - use 'in' operator
+          const primaryKey = tableDefinition.primaryKeys[0];
+          const ids = fixtures.map(fixture => fixture.data[primaryKey]);
+          const { data, error } = await supabase2.from(table).delete().in(primaryKey, ids).select();
+          if (error) {
+            log?.error("Error deleting records", { error });
+            throw new Error(`Error deleting records: ${error.message}`);
           }
+          log?.debug(`Deleted ${data?.length} records from ${qualifiedTable}`);
+        } else {
+          // Composite primary key - fall back to OR queries but batch them
+          const BATCH_SIZE = 10;
+          let totalDeleted = 0;
+
+          for (let i = 0; i < fixtures.length; i += BATCH_SIZE) {
+            const batch = fixtures.slice(i, i + BATCH_SIZE);
+            let deletionQuery = supabase2.from(table).delete();
+
+            const filterStrings = [];
+            for (const fixture of batch) {
+              const filterString = tableDefinition.primaryKeys.map((key) => `${key}.eq.${fixture.data[key]}`).join(",");
+              filterStrings.push(`and(${filterString})`);
+            }
+            deletionQuery = deletionQuery.or(filterStrings.join(","));
+
+            const { data, error } = await deletionQuery.select();
+            if (error) {
+              log?.error("Error deleting records", { error });
+              throw new Error(`Error deleting records: ${error.message}`);
+            }
+            totalDeleted += data?.length || 0;
+          }
+          log?.debug(`Deleted ${totalDeleted} records from ${qualifiedTable}`);
         }
-        deletionQuery = deletionQuery.or(filterStrings.join(","));
       } else {
         throw new Error(
           `Cannot delete records from table ${qualifiedTable} as it has no primary key`
         );
       }
-      const { data, error } = await deletionQuery.select();
-      if (error) {
-        log?.error("Error deleting records", { error });
-        throw new Error(`Error deleting records: ${error.message}`);
-      }
-      log?.debug(`Deleted ${data?.length} records from ${qualifiedTable}`);
     }
     const authRecordsToRemove = this.fixtures("auth", "users").map(
       (fixture) => fixture.data.id
