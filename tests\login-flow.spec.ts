import { expect } from '@playwright/test'
import { test } from './utils/test'
import { unlockProtectedArea } from './utils/unlock-protected-area'
import { createUser } from './utils/user-helpers'

test.describe('Login Tests', () => {
  test.beforeEach(async ({ page }) => {
    await unlockProtectedArea(page)
    await page.goto('/login')
  })

  test('should load login page correctly', async ({ page }) => {
    // Check that the login form is visible
    await expect(page.locator('text=Welcome Back')).toBeVisible()
    await expect(
      page.locator('text=Sign in to access your AI Readiness Dashboard'),
    ).toBeVisible()

    // Check that form elements are present and accessible
    await expect(page.locator('#email')).toBeVisible()
    await expect(page.locator('#password')).toBeVisible()
    await expect(page.getByRole('button', { name: 'Sign In' })).toBeVisible()
  })

  test('should show form validation for empty fields', async ({ page }) => {
    // Try to submit empty form
    const signInButton = page.getByRole('button', { name: 'Sign In' })
    await signInButton.click()

    // Check if HTML5 validation kicks in (required fields)
    const emailInput = page.locator('#email')
    const isEmailInvalid = await emailInput.evaluate(
      (el: HTMLInputElement) => !el.validity.valid,
    )

    expect(isEmailInvalid).toBe(true)
  })

  test('should show error message for invalid credentials', async ({
    page,
  }) => {
    // Fill with invalid credentials
    const emailInput = page.locator('#email')
    const passwordInput = page.locator('#password')
    const signInButton = page.getByRole('button', { name: 'Sign In' })

    await emailInput.fill('<EMAIL>')
    await passwordInput.fill('wrongpassword')
    await signInButton.click()

    // Wait for error to appear
    await page.waitForTimeout(3000)

    // Should show an error message
    const errorAlert = page.locator('[role="alert"]')
    await expect(errorAlert).toBeVisible()

    // Check that the error message contains relevant text
    const errorText = await errorAlert.textContent()
    expect(errorText).toMatch(/invalid|credentials/i)

    // Should still be on login page
    await expect(page).toHaveURL(/.*\/login/)
  })

  test('should successfully login with valid credentials', async ({
    page,
    supawright,
  }) => {
    const { user } = await createUser(supawright)

    // Fill in valid credentials
    await page.locator('#email').fill(user.email!)
    await page.locator('#password').fill('123456')
    await page.getByRole('button', { name: 'Sign In' }).click()

    await page.waitForLoadState('networkidle')
    await expect(page).not.toHaveURL(/.*\/login/)
  })
})
