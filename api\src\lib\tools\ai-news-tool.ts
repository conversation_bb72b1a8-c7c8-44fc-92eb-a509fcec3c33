/* eslint-disable prettier/prettier */
import { tool } from 'ai'
import { z } from 'zod'

// Define news source types
const NewsSourceType = z
  .string()
  .describe(
    'Type of news source. eg Newsletter, Blog, Podcast, YouTube Channel, Twitter, LinkedIn, Reddit, Other',
  )

// Define frequency options
const NewsFrequency = z.enum([
  'MULTIPLE_TIMES_DAILY',
  'DAILY',
  'SEVERAL_TIMES_WEEKLY',
  'WEEKLY',
  'BIWEEKLY',
  'MONTHLY',
  'OCCASIONALLY',
  'RARELY',
])
// Define AI domains of interest
const AIDomain = z.string()

const NewsSource = z.object({
  name: z
    .string()
    .describe(
      'Name of the news source (e.g., "The Batch by <PERSON>", "<PERSON> Podcast")',
    ),
  type: NewsSourceType.describe('Type of news source'),
  topics: z.array(AIDomain).describe('AI domains this source covers'),
  description: z
    .string()
    .describe('Additional context about this source mentioned by the user'),
})

const NewsConsumptionHabits = z.object({
  overallFrequency: NewsFrequency.describe(
    'Overall frequency of AI news consumption',
  ),

  timeSpent: z
    .string()
    .describe('How much time user spends on AI news daily/weekly'),
  consumptionMethod: z
    .string()
    .describe(
      'How the user typically consumes AI news (mobile, desktop, during commute, etc.)',
    ),
  informationOverload: z
    .boolean()
    .describe('Whether the user feels overwhelmed by the amount of AI news'),
  challenges: z
    .string()
    .describe('Challenges the user faces in staying updated with AI news'),
})

export const AINewsTool = tool({
  description: `Use this tool to gather information about the user's AI news consumption habits and preferences.
  Talk about this point after you finish talking about other important topics.

  Ask follow-up questions to understand:
  - What specific AI news sources they follow
  - How often they consume AI news, whether it helps them.
  - What types of AI content they find most valuable
  - Whether they feel they're staying adequately informed
  - Any challenges in filtering relevant information
  
  Examples of what to capture:
  - "I read The Batch newsletter weekly"
  - "I follow Andrej Karpathy on Twitter"
  - "I listen to Lex Fridman podcast occasionally"
  - "I spend about 30 minutes daily reading AI news"
  - "I'm interested in generative AI and NLP developments"
  - "Sometimes I feel overwhelmed by all the AI news"
  
  ❌NEVER EVER mention which tool is being used to user.
  ❌NEVER ask more than two questions in a message
  ✅ALWAYS Put each sentence on a new line for better readability
  📌Keeping conversation friendly, but professional is essential
  ✅Be curious - ask 1-2 follow-ups per topic to extract richer details.
  ✅Generally react to user's reply in 10 words, then ask further questions and ❌ Avoid writing messages longer than 50 words
  Example:
  AI: "How do you manage to stay up to date about AI? Do you like to do so?"
  User: "I read The Batch newsletter weekly"
  AI:"That must sharpen your knowledge. Does it help in your work?"
  `,
  inputSchema: z.object({
    newsSources: z
      .array(NewsSource)
      .describe('Array of AI news sources mentioned by the user'),
    consumptionHabits: NewsConsumptionHabits.describe(
      "User's AI news consumption patterns and preferences",
    ),
    extractedFromMessage: z
      .string()
      .describe('The user message this information was extracted from'),
  }),

  execute: async ({ newsSources, consumptionHabits, extractedFromMessage }) => {
    const aiNewsData = {
      newsSources,
      consumptionHabits,
      extractedFromMessage,
      timestamp: new Date().toISOString(),
    }

    console.log(
      'AI news consumption data gathered:',
      JSON.stringify(aiNewsData, null, 2),
    )

    const sourceNames = newsSources.map((source) => source.name).join(', ')

    return `Gathered AI news consumption information. Sources mentioned: ${sourceNames || 'none specified'}.. Thank you for sharing your AI news habits!`
  },
})
