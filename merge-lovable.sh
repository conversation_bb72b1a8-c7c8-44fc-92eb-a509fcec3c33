#!/bin/bash
set -e

# === CONFIGURATION ===
BRANCH_NAME="simon/lovable"
EXCLUDED_FILES=(
  "package.json"
  "vite.config.ts"
  "src/lib/api.ts"
)

# === STEP 0: Fetch latest from origin ===
echo "🌐 Fetching from origin..."
git fetch origin

# === STEP 1: Update local main ===
echo "📥 Updating local main..."
git checkout main
git branch --set-upstream-to=origin/main main
git pull

# === STEP 2: Merge main into the feature branch ===
echo "🔄 Merging main into $BRANCH_NAME..."
git checkout "$BRANCH_NAME"
git merge main -m "Update lovable branch from main"

# Optional: abort if conflicts occurred
if [ -n "$(git ls-files -u)" ]; then
  echo "❌ Merge conflicts detected. Resolve them before rerunning this script."
  exit 1
fi

# === STEP 3: Back to main for squash merge ===
echo "🔀 Switching back to main for squash merge..."
git checkout main

echo "📦 Squash-merging $BRANCH_NAME into main..."
git merge --squash "$BRANCH_NAME"

# === STEP 4: Revert and unstage excluded files ===
echo "🚫 Reverting and unstaging excluded files:"
for file in "${EXCLUDED_FILES[@]}"; do
  echo "  ↩️  $file"
  git checkout HEAD -- "$file"     # content back to main
  git reset HEAD "$file"           # unstage explicitly
done

# === STEP 5: Final commit ===
echo "✅ Creating squash commit..."
git commit -m "Squash-merged $BRANCH_NAME into main (excluding env-specific files)"

echo "🚀 Done. main now includes updated $BRANCH_NAME (with latest main merged in)."