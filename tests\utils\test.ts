import { withSupawright } from 'supawright'
import type { Database } from '../../api/src/types/database'
import { EmailTestHelper } from './email-testing'

// Base test with Su<PERSON>wright
const base = withSupawright<Omit<Database, '__InternalSupabase'>, 'public'>([
  'public',
])

// Extended test with email testing fixture
export const test = base.extend<{ readonly email: EmailTestHelper }>({
  // eslint-disable-next-line no-empty-pattern
  email: async ({}, use) => {
    const email = new EmailTestHelper()

    // Clear messages before each test
    await email.clearMessages()

    // eslint-disable-next-line react-hooks/rules-of-hooks
    await use(email)

    // Optional: Clear messages after each test for cleanup
    await email.clearMessages()
  },
})

export type TestArgs = Parameters<Parameters<typeof test>[2]>[0]
