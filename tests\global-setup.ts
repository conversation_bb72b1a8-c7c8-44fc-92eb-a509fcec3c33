import { setupGlobalTestEnv, setupE2eTest } from './config/environment'

async function globalSetup() {
  console.log('🚀 Starting global test setup...')

  // Set up environment variables for tests using centralized configuration
  setupGlobalTestEnv()

  try {
    // Setup the test environment once for all tests
    // This starts Supabase (if not already running) and resets the database
    await setupE2eTest()
    console.log('✅ Supabase test environment ready')

    // Give Supabase a moment to fully initialize
    await new Promise((resolve) => setTimeout(resolve, 2000))
    console.log('✅ Global setup complete')
  } catch (error) {
    console.error('❌ Failed to setup test environment:', error)
    console.error('Make sure Docker is running and Supabase CLI is installed')
    throw error
  }
}

export default globalSetup
