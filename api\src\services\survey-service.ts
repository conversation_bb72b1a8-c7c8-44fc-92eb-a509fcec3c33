/* eslint-disable prettier/prettier */
import { supabaseAdmin } from '../lib/supabase.js'
import {
  SurveyRepository,
  type SurveyResponse,
} from '../repositories/survey-repository.js'
import type { SurveyAnswer, SurveyData, Message } from '../types/survey.js'
import type { CategoryScores } from '../types/scores.js'
import { ThresholdService } from './threshold-service.js'

// ✅ Define the CompanyToolData interface
export interface CompanyToolData {
  tool_and_usecases: Array<{
    tool: string
    use_case: string
  }>
  level: string | null
}

export class SurveyService {
  
  getProfileByUserId(userId: string) {
    throw new Error('Method not implemented.')
  }
  getSurveysByCompanyName(companyName: string) {
    throw new Error('Method not implemented.')
  }
  async fetchAndStoreCompanyData(userId: string): Promise<CompanyToolData | null> {
    try {
      // 1. Fetch the user profile for the given ID
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .maybeSingle()

      if (profileError) {
        console.error('Error fetching user profile:', profileError)
        throw profileError
      }

      if (!profile) {
        throw new Error(`User profile not found for ID: ${userId}`)
      }

      // 2. Fetch the company using the user profile's company_id
      if (!profile.company_id) {
        throw new Error(`User profile has no company_id for ID: ${userId}`)
      }

      const { data: company, error: companyError } = await supabaseAdmin
        .from('companies')
        .select('*')
        .eq('id', profile.company_id)
        .maybeSingle()

      if (companyError) {
        console.error('Error fetching company:', companyError)
        throw companyError
      }

      if (!company) {
        throw new Error(`Company not found for ID: ${profile.company_id}`)
      }

      // 3. Fetch all completed survey responses for this company
      const { data: surveyResponses, error: surveyError } = await supabaseAdmin
        .from('survey_responses')
        .select('*')
        .eq('company_id', company.id)
        .not('completed_at', 'is', null)
        .order('completed_at', { ascending: false })

      if (surveyError) {
        console.error('Error fetching survey responses:', surveyError)
        throw surveyError
      }

      // Process tool and use case data
      const toolAndUseCases: Array<{ tool: string; use_case: string }> = []

      if (surveyResponses && Array.isArray(surveyResponses)) {
        surveyResponses.forEach((response, index) => {
          try {
            let tools: string[] = []
            let useCases: string[] = []

            if (response.tools) {
              if (typeof response.tools === 'string') {
                try {
                  tools = JSON.parse(response.tools)
                } 
                catch (e) {
                  console.warn(`Failed to parse tools for response ${index}:`, response.tools)
                  tools = []
                }
              } else if (Array.isArray(response.tools)) {
                tools = response.tools.map((item) => String(item))
              }
            }

            if (response.use_cases) {
              if (typeof response.use_cases === 'string') {
                try {
                  useCases = JSON.parse(response.use_cases)
                } catch (e) {
                  console.warn(`Failed to parse use_cases for response ${index}:`, response.use_cases)
                  useCases = []
                }
              } else if (Array.isArray(response.use_cases)) {
                useCases = response.use_cases.map((item) => String(item))
              }
            }

            if (Array.isArray(tools) && Array.isArray(useCases) && tools.length > 0 && useCases.length > 0) {
              tools.forEach((tool) => {
                if (tool && typeof tool === 'string') {
                  useCases.forEach((useCase) => {
                    if (useCase && typeof useCase === 'string') {
                      const exists = toolAndUseCases.some(item =>
                        item.tool.toLowerCase().trim() === tool.toLowerCase().trim() &&
                        item.use_case.toLowerCase().trim() === useCase.toLowerCase().trim()
                      )
                      if (!exists) {
                        toolAndUseCases.push({
                          tool: tool.trim(),
                          use_case: useCase.trim()
                        })
                      }
                    }
                  })
                }
              })
            }
          } catch (processingError) {
            console.error(`Error processing response ${index}:`, processingError, response)
          }
        })
      }

      return {
        tool_and_usecases: toolAndUseCases,
        level: profile.level || null
      }
    } catch (error) {
      console.error('Error fetching company data:', error)
      return null
    }
  }

  static getStoredCompanyData(): unknown {
    throw new Error('Method not implemented.')
  }
  private readonly surveyRepository: SurveyRepository
  private readonly thresholdService: ThresholdService

  constructor() {
    this.surveyRepository = new SurveyRepository()
    this.thresholdService = new ThresholdService()
  }

  /**
   * Get user's surveys (both drafts and completed) with draft property
   * Ordered with drafts first, then completed surveys by completion date descending
   */
  async getSurveys(
    userId: string,
  ): Promise<(SurveyResponse & { draft: boolean })[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('survey_responses')
        .select('*')
        .eq('user_id', userId)

      if (error) throw error

      const surveys = (data || []).map((survey) => ({
        ...survey,
        draft: !survey.completed_at,
      }))

      // Sort: drafts first (by updated_at desc), then completed (by completed_at desc)
      return surveys.sort((a, b) => {
        // If one is draft and other is not, draft comes first
        if (a.draft && !b.draft) return -1
        if (!a.draft && b.draft) return 1

        // If both are drafts, sort by updated_at descending
        if (a.draft && b.draft) {
          return (
            new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
          )
        }

        // If both are completed, sort by completed_at descending
        if (!a.draft && !b.draft && a.completed_at && b.completed_at) {
          return (
            new Date(b.completed_at).getTime() -
            new Date(a.completed_at).getTime()
          )
        }

        return 0
      })
    } catch (error) {
      console.error('Error fetching surveys:', error)
      throw error
    }
  }

  /**
   * Get user's last survey with score - Original API method
   */
  async getLastScore(userId: string) {
    try {
      const { data, error } = await supabaseAdmin
        .from('survey_responses')
        .select(
          `
          id,
          overall_score,
          category_scores,
          responses,
          chat_history,
          updated_at,
          completed_at,
          profiles!fk_survey_responses_user_id (
            level,
            companies!profiles_company_id_fkey (
              name,
              industry,
              employee_count,
              country
            )
          )
        `,
        )
        .eq('user_id', userId)
        .not('completed_at', 'is', null)
        .order('completed_at', { ascending: false })
        .limit(1)
        .maybeSingle()

      if (error) throw error

      if (!data) {
        return null
      }

      const profile = data.profiles
      const company = profile?.companies

      return {
        id: data.id,
        overall_score: data.overall_score || 0,
        category_scores: (data.category_scores || {}) as CategoryScores,
        profile: profile
          ? {
              level: profile.level,
              company_name: company?.name || '',
              industry: company?.industry,
              employee_count: company?.employee_count,
              country: company?.country,
            }
          : null,
        survey: {
          id: data.id,
          responses: data.responses || [],
          chat_history: data.chat_history || [],
          category_scores: (data.category_scores || {}) as CategoryScores,
          overall_score: data.overall_score || 0,
          updated_at: data.updated_at,
          completed_at: data.completed_at,
        },
      }
    } catch (error) {
      console.error('Error fetching last score:', error)
      throw error
    }
  }

  /**
   * Check if user has surveys - Original API method
   */
  async hasSurveys(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabaseAdmin
        .from('survey_responses')
        .select('id')
        .eq('user_id', userId)
        .not('completed_at', 'is', null)
        .limit(1)

      if (error) throw error
      return (data?.length || 0) > 0
    } catch (error) {
      console.error('Error checking surveys:', error)
      throw error
    }
  }

  async saveSurvey(surveyData: SurveyData, sessionId: string, userId?: string) {
    try {
      const surveyId = await this.surveyRepository.saveSurvey(
        surveyData,
        sessionId,
        userId,
      )

      if (userId && surveyData.completed_at) {
        await this.thresholdService.checkAndNotifyThresholds(userId)
      }

      return surveyId
    } catch (error) {
      console.error('Error saving survey:', error)
      throw error
    }
  }

  /**
   * Get survey by ID
   */
  async getSurvey(userId: string, surveyId: string) {
    try {
      return await this.surveyRepository.getSurvey(userId, surveyId)
    } catch (error) {
      console.error('Error fetching survey:', error)
      throw error
    }
  }

  /**
   * Get survey by session ID
   */
  async getSurveyBySessionId(userId: string | undefined, sessionId: string) {
    try {
      return await this.surveyRepository.getSurveyBySessionId(userId, sessionId)
    } catch (error) {
      console.error('Error fetching survey by session:', error)
      throw error
    }
  }

  /**
   * Get score with profile data
   */
  async getScore(userId: string, surveyId: string) {
    try {
      const score = await this.surveyRepository.getScoreWithProfile(
        userId,
        surveyId,
      )
      const survey = await this.surveyRepository.getSurvey(userId, surveyId)
      if (!score || !survey) return null
      return { ...score, survey }
    } catch (error) {
      console.error('Error fetching score:', error)
      throw error
    }
  }

  /**
   * Get user's last score with survey data (repository version)
   */
  async getLastScoreWithSurvey(userId: string) {
    try {
      const surveys = await this.surveyRepository.getSurveys(userId)
      const lastScore =
        await this.surveyRepository.getLastScoreWithProfile(userId)

      if (!lastScore) return null

      return {
        ...lastScore,
        survey: surveys && surveys.length > 0 ? surveys[0] : ({} as SurveyData),
      }
    } catch (error) {
      console.error('Error fetching last score with survey:', error)
      throw error
    }
  }

  /**
   * Get surveys as SurveyData objects (repository version)
   */
  async getSurveysAsData(userId: string): Promise<SurveyData[]> {
    try {
      return await this.surveyRepository.getSurveys(userId)
    } catch (error) {
      console.error('Error fetching surveys as data:', error)
      throw error
    }
  }

  /**
   * Get company-wide survey statistics
   */
  async getCompanyStats(userId: string) {
    try {
      // Get user's company ID
      const { data: profile } = await supabaseAdmin
        .from('profiles')
        .select(
          'company_id, companies!profiles_company_id_fkey(name, industry)',
        )
        .eq('user_id', userId)
        .single()

      if (!profile?.company_id) {
        return {
          userStats: { totalAssessments: 0, latestScore: null },
          companyStats: {
            totalAssessments: 0,
            averageScore: null,
            companyName: null,
          },
          maturityLevel: null,
        }
      }

      // Get user's completed surveys count and latest score
      const { data: userSurveys } = await supabaseAdmin
        .from('survey_responses')
        .select('overall_score')
        .eq('user_id', userId)
        .not('completed_at', 'is', null)
        .order('completed_at', { ascending: false })

      // Get company-wide completed surveys
      const { data: companySurveys } = await supabaseAdmin
        .from('survey_responses')
        .select('overall_score, user_id')
        .eq('company_id', profile.company_id)
        .not('completed_at', 'is', null)

      const userTotalAssessments = userSurveys?.length || 0
      const userLatestScore = userSurveys?.[0]?.overall_score || null

      const companyTotalAssessments = companySurveys?.length || 0
      const companyAverageScore = companySurveys?.length
        ? companySurveys.reduce(
            (sum, survey) => sum + (survey.overall_score || 0),
            0,
          ) / companySurveys.length
        : null

      // Calculate maturity level based on industry benchmarks (simplified)
      let maturityLevel = null
      if (userLatestScore && profile.companies?.industry) {
        const score = userLatestScore * 100
        if (score >= 85) maturityLevel = 'Innovator'
        else if (score >= 70) maturityLevel = 'Advanced'
        else if (score >= 55) maturityLevel = 'Intermediate'
        else if (score >= 40) maturityLevel = 'Developing'
        else maturityLevel = 'Laggard'
      }

      return {
        userStats: {
          totalAssessments: userTotalAssessments,
          latestScore: userLatestScore,
        },
        companyStats: {
          totalAssessments: companyTotalAssessments,
          averageScore: companyAverageScore,
          companyName: profile.companies?.name || null,
        },
        maturityLevel,
      }
    } catch (error) {
      console.error('Error fetching company stats:', error)
      throw error
    }
  }

  /**
   * Get survey details for editing
   */
  async getSurveyDetails(surveyId: string, userId: string) {
    try {
      const { data, error } = await supabaseAdmin
        .from('survey_responses')
        .select('*')
        .eq('id', surveyId)
        .eq('user_id', userId)
        .single()

      if (error) throw error
      if (!data) throw new Error('Survey not found or access denied')

      return {
        id: data.id,
        responses: (data.responses || []) as unknown as SurveyAnswer[],
        chat_history: (data.chat_history || []) as unknown as Message[],
        category_scores: (data.category_scores || {}) as CategoryScores,
        overall_score: data.overall_score || 0,
        updated_at: data.updated_at,
        completed_at: data.completed_at,
        is_draft: !data.completed_at,
      }
    } catch (error) {
      console.error('Error fetching survey details:', error)
      throw error
    }
  }

  /**
   * Update survey responses
   */
  async updateSurvey(
    userId: string,
    surveyId: string,
    surveyData: Pick<
      SurveyData,
      | 'responses'
      | 'chat_history'
      | 'category_scores'
      | 'overall_score'
      | 'completed_at'
    >,
  ) {
    return this.surveyRepository.updateSurvey(userId, surveyId, surveyData)
  }

  /**
   * Delete survey (drafts only)
   */
  async deleteSurvey(surveyId: string, userId: string) {
    try {
      // First check if the survey exists and belongs to the user
      const { data: survey, error: fetchError } = await supabaseAdmin
        .from('survey_responses')
        .select('id, completed_at, user_id')
        .eq('id', surveyId)
        .eq('user_id', userId)
        .single()

      if (fetchError) throw fetchError
      if (!survey) throw new Error('Survey not found or access denied')

      // Only allow deletion of draft surveys (completed_at is null)
      if (survey.completed_at) {
        throw new Error('Cannot delete completed surveys')
      }

      // Delete the survey
      const { error: deleteError } = await supabaseAdmin
        .from('survey_responses')
        .delete()
        .eq('id', surveyId)
        .eq('user_id', userId)

      if (deleteError) throw deleteError
    } catch (error) {
      console.error('Error deleting survey:', error)
      throw error
    }
  }

  /**
   * Associate survey responses with user only if no user is currently associated
   * After that it checks and notifies thresholds
   */
  async associateSurveySessionWithUser(userId: string, sessionId: string) {
    await this.surveyRepository.associateSurveySessionWithUser(
      userId,
      sessionId,
    )

    // Check and notify thresholds, since this only happens after a user signs up. therefore this user could be the last survey needed to meet the threshold
    await this.thresholdService.checkAndNotifyThresholds(userId)
  }
}
